// API客户端工具类
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: string[];
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface Tool {
  _id: string;
  name: string;
  tagline?: string;
  description: string;
  longDescription?: string;
  website: string;
  logo?: string;
  category: string;
  tags: string[];
  pricing: 'free' | 'freemium' | 'paid';
  pricingDetails?: string;
  screenshots?: string[];
  submittedBy: string;
  submittedAt: string;
  publishedAt?: string;
  status: 'pending' | 'approved' | 'rejected';
  reviewNotes?: string;
  reviewedBy?: string;
  reviewedAt?: string;
  views: number;
  likes: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ToolsResponse {
  tools: Tool[];
  pagination: PaginationInfo;
  stats?: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  };
}

export interface AdminStats {
  overview: {
    totalTools: number;
    pendingTools: number;
    approvedTools: number;
    rejectedTools: number;
    totalViews: number;
    totalLikes: number;
    recentSubmissions: number;
    recentApprovals: number;
    recentRejections: number;
    avgReviewTime: number;
  };
  categoryStats: Array<{
    _id: string;
    count: number;
    totalViews: number;
    totalLikes: number;
  }>;
  topTools: Array<{
    _id: string;
    name: string;
    category: string;
    views: number;
    likes: number;
  }>;
  recentActivity: Array<{
    _id: string;
    name: string;
    submittedBy: string;
    submittedAt: string;
    status: string;
    reviewedAt?: string;
    reviewedBy?: string;
  }>;
  dailyStats: Array<{
    date: string;
    day: string;
    submissions: number;
    approvals: number;
    rejections: number;
  }>;
  timeRange: string;
}

export interface Category {
  id: string;
  name: string;
  count: number;
  totalViews: number;
  totalLikes: number;
}

export interface CategoriesResponse {
  categories: Category[];
  overview: {
    totalTools: number;
    totalViews: number;
    totalLikes: number;
  };
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const config: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      };

      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败',
      };
    }
  }

  // 工具相关API
  async getTools(params?: {
    page?: number;
    limit?: number;
    category?: string;
    status?: string;
    search?: string;
    sort?: string;
    order?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<ToolsResponse>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    const query = searchParams.toString();
    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);
  }

  async getTool(id: string): Promise<ApiResponse<Tool>> {
    return this.request<Tool>(`/tools/${id}`);
  }

  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {
    return this.request<Tool>('/tools', {
      method: 'POST',
      body: JSON.stringify(toolData),
    });
  }

  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {
    return this.request<Tool>(`/tools/${id}`, {
      method: 'PUT',
      body: JSON.stringify(toolData),
    });
  }

  async deleteTool(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/tools/${id}`, {
      method: 'DELETE',
    });
  }

  // 管理员API
  async getAdminTools(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    sort?: string;
    order?: string;
  }): Promise<ApiResponse<ToolsResponse>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    const query = searchParams.toString();
    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);
  }

  async approveTool(id: string, data: {
    reviewedBy?: string;
    reviewNotes?: string;
    publishedAt?: string;
  }): Promise<ApiResponse<Tool>> {
    return this.request<Tool>(`/admin/tools/${id}/approve`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async rejectTool(id: string, data: {
    reviewedBy?: string;
    rejectReason: string;
  }): Promise<ApiResponse<Tool>> {
    return this.request<Tool>(`/admin/tools/${id}/reject`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {
    const query = timeRange ? `?timeRange=${timeRange}` : '';
    return this.request<AdminStats>(`/admin/stats${query}`);
  }

  // 分类API
  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {
    return this.request<CategoriesResponse>('/categories');
  }
}

// 创建默认的API客户端实例
export const apiClient = new ApiClient();

// 导出API客户端类以便在需要时创建新实例
export { ApiClient };
