'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import ToolCard from '@/components/ToolCard';
import CategoryCard from '@/components/CategoryCard';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import LoginModal from '@/components/auth/LoginModal';
import { apiClient, Tool } from '@/lib/api';
import { Search, TrendingUp, Star, Zap, Calendar, Clock } from 'lucide-react';

// 分类数据

const categories = [
  {
    _id: '1',
    name: '文本生成',
    slug: 'text-generation',
    description: 'AI tools for generating and editing text content',
    icon: '📝',
    color: '#3B82F6',
    toolCount: 25
  },
  {
    _id: '2',
    name: '图像生成',
    slug: 'image-generation',
    description: 'AI tools for creating and editing images',
    icon: '🎨',
    color: '#8B5CF6',
    toolCount: 18
  },
  {
    _id: '3',
    name: '代码生成',
    slug: 'code-generation',
    description: 'AI tools for writing and debugging code',
    icon: '💻',
    color: '#F59E0B',
    toolCount: 12
  },
  {
    _id: '4',
    name: '数据分析',
    slug: 'data-analysis',
    description: 'AI tools for analyzing and visualizing data',
    icon: '📊',
    color: '#06B6D4',
    toolCount: 15
  }
];

export default function Home() {
  const [featuredTools, setFeaturedTools] = useState<Tool[]>([]);
  const [todayTools, setTodayTools] = useState<Tool[]>([]);
  const [recentTools, setRecentTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError('');

      // 准备日期参数
      const today = new Date();
      const todayStart = new Date(today);
      todayStart.setHours(0, 0, 0, 0);
      const todayEnd = new Date(today);
      todayEnd.setHours(23, 59, 59, 999);

      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      sevenDaysAgo.setHours(0, 0, 0, 0);

      // 并行获取所有数据
      const [featuredResponse, todayResponse, recentResponse] = await Promise.all([
        // 热门工具 - 按浏览量排序
        apiClient.getTools({
          status: 'approved',
          limit: 6,
          sort: 'views',
          order: 'desc'
        }),
        // 当日发布的工具 - 使用日期筛选
        apiClient.getTools({
          status: 'approved',
          limit: 20,
          sort: 'publishedAt',
          order: 'desc',
          dateFrom: todayStart.toISOString(),
          dateTo: todayEnd.toISOString()
        }),
        // 最近发布的工具 - 使用日期筛选
        apiClient.getTools({
          status: 'approved',
          limit: 20,
          sort: 'publishedAt',
          order: 'desc',
          dateFrom: sevenDaysAgo.toISOString()
        })
      ]);

      if (featuredResponse.success && featuredResponse.data) {
        setFeaturedTools(featuredResponse.data.tools);
      }

      if (todayResponse.success && todayResponse.data) {
        setTodayTools(todayResponse.data.tools.slice(0, 6));
      }

      if (recentResponse.success && recentResponse.data) {
        setRecentTools(recentResponse.data.tools.slice(0, 6));
      }

    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Error Message */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
          <ErrorMessage
            message={error}
            onClose={() => setError('')}
          />
        </div>
      )}

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              发现最好的
              <span className="text-blue-600"> AI 工具</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美工具。
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-8">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索 AI 工具、分类或功能..."
                  className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg"
                />
                <Search className="absolute left-4 top-4 h-6 w-6 text-gray-400" />
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/tools"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <Zap className="mr-2 h-5 w-5" />
                浏览所有工具
              </Link>
              <Link
                href="/submit"
                className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                提交您的工具
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Today's Tools Section */}
      {todayTools.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                <Calendar className="inline-block mr-2 h-8 w-8 text-green-600" />
                今日发布
              </h2>
              <p className="text-lg text-gray-600">
                今天刚刚发布的最新 AI 工具
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {todayTools.map((tool) => (
                <ToolCard
                  key={tool._id}
                  tool={tool}
                  onLoginRequired={() => setIsLoginModalOpen(true)}
                />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Recent Tools Section */}
      {recentTools.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                <Clock className="inline-block mr-2 h-8 w-8 text-blue-600" />
                最近发布
              </h2>
              <p className="text-lg text-gray-600">
                过去一周内发布的新 AI 工具
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentTools.map((tool) => (
                <ToolCard
                  key={tool._id}
                  tool={tool}
                  onLoginRequired={() => setIsLoginModalOpen(true)}
                />
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href="/tools?sort=publishedAt&order=desc"
                className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
              >
                查看更多最新工具
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Featured Tools Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              <TrendingUp className="inline-block mr-2 h-8 w-8 text-blue-600" />
              热门工具
            </h2>
            <p className="text-lg text-gray-600">
              最受欢迎和评价最高的 AI 工具
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredTools.map((tool) => (
              <ToolCard
                key={tool._id}
                tool={tool}
                onLoginRequired={() => setIsLoginModalOpen(true)}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/tools"
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              查看更多工具
            </Link>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              <Star className="inline-block mr-2 h-8 w-8 text-blue-600" />
              热门分类
            </h2>
            <p className="text-lg text-gray-600">
              按功能分类浏览 AI 工具
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <CategoryCard key={category._id} category={category} />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/categories"
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              查看所有分类
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-white mb-2">500+</div>
              <div className="text-blue-100">AI 工具</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-white mb-2">50+</div>
              <div className="text-blue-100">工具分类</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-white mb-2">10K+</div>
              <div className="text-blue-100">用户访问</div>
            </div>
          </div>
        </div>
      </section>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Layout>
  );
}
