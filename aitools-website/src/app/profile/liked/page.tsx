'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import ToolCard from '@/components/ToolCard';
import { apiClient, Tool } from '@/lib/api';
import {
  Heart,
  ArrowLeft,
  Search,
  Filter
} from 'lucide-react';

// 分类标签映射
const categoryLabels: Record<string, string> = {
  'text-generation': '文本生成',
  'image-generation': '图像生成',
  'code-generation': '代码生成',
  'data-analysis': '数据分析',
  'audio-processing': '音频处理',
  'video-editing': '视频编辑',
  'design-tools': '设计工具',
  'productivity': '生产力工具',
  'customer-service': '客户服务'
};

export default function LikedToolsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [likedTools, setLikedTools] = useState<Tool[]>([]);
  const [filteredTools, setFilteredTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }
    
    if (status === 'authenticated') {
      fetchLikedTools();
    }
  }, [status, router]);

  useEffect(() => {
    filterTools();
  }, [likedTools, searchQuery, selectedCategory]);

  const fetchLikedTools = async () => {
    try {
      setLoading(true);
      setError('');

      // TODO: 实现获取用户点赞的工具API
      // 暂时使用示例数据
      const response = await apiClient.getTools({ status: 'approved', limit: 20 });
      
      if (response.success && response.data) {
        // 模拟用户点赞的工具（实际应该从用户点赞记录中获取）
        const mockLikedTools = response.data.tools.slice(0, 8);
        setLikedTools(mockLikedTools);
      } else {
        setError(response.error || '获取收藏列表失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filterTools = () => {
    let filtered = likedTools;

    // 按搜索关键词过滤
    if (searchQuery) {
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category === selectedCategory);
    }

    setFilteredTools(filtered);
  };

  const handleUnlike = async (toolId: string) => {
    try {
      console.log('handleUnlike called for tool:', toolId);
      console.log('Current likedTools count:', likedTools.length);
      setLikedTools(prev => {
        const filtered = prev.filter(tool => tool._id !== toolId);
        console.log('New likedTools count after filter:', filtered.length);
        return filtered;
      });
    } catch (error) {
      console.error('Error unliking tool:', error);
    }
  };

  // 获取所有分类
  const categories = Array.from(new Set(likedTools.map(tool => tool.category)));

  if (status === 'loading' || loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <LoadingSpinner size="lg" className="py-20" />
        </div>
      </Layout>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <div className="flex items-center mb-2">
              <Link
                href="/profile"
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">我的收藏</h1>
            </div>
            <p className="text-lg text-gray-600">您收藏的AI工具 ({likedTools.length})</p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="搜索收藏的工具..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="sm:w-48">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Filter className="h-5 w-5 text-gray-400" />
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">所有分类</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {categoryLabels[category] || category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <ErrorMessage
            message={error}
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Tools Grid */}
        {filteredTools.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTools.map((tool) => (
              <ToolCard
                key={tool._id}
                tool={tool}
                onUnlike={handleUnlike}
              />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <div className="text-gray-400 mb-4">
              <Heart className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || selectedCategory !== 'all' ? '没有找到匹配的工具' : '还没有收藏任何工具'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || selectedCategory !== 'all' 
                ? '尝试调整搜索条件或筛选器'
                : '开始探索并收藏您喜欢的AI工具吧！'
              }
            </p>
            {(!searchQuery && selectedCategory === 'all') && (
              <Link
                href="/tools"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                浏览工具
              </Link>
            )}
          </div>
        )}
      </div>
    </Layout>
  );
}
