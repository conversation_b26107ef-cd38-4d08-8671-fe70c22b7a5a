{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,qBACE,8OAAC;QAAI,WAAU;;0BAGb,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport {\n  User,\n  Mail,\n  Bell,\n  Shield,\n  Eye,\n  Save,\n  ArrowLeft,\n  Camera,\n  Trash2\n} from 'lucide-react';\n\nexport default function SettingsPage() {\n  const { data: session, status, update } = useSession();\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  \n  // 用户信息表单\n  const [profileForm, setProfileForm] = useState({\n    name: '',\n    email: '',\n    bio: '',\n    website: '',\n    location: ''\n  });\n\n  // 通知设置\n  const [notificationSettings, setNotificationSettings] = useState({\n    emailNotifications: true,\n    toolApprovalNotifications: true,\n    weeklyDigest: false,\n    marketingEmails: false\n  });\n\n  // 隐私设置\n  const [privacySettings, setPrivacySettings] = useState({\n    profileVisibility: 'public',\n    showEmail: false,\n    showSubmittedTools: true\n  });\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/');\n      return;\n    }\n    \n    if (status === 'authenticated' && session?.user) {\n      setProfileForm({\n        name: session.user.name || '',\n        email: session.user.email || '',\n        bio: '',\n        website: '',\n        location: ''\n      });\n    }\n  }, [status, session, router]);\n\n  const handleProfileSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // TODO: 实现更新用户资料API\n      // const response = await apiClient.updateProfile(profileForm);\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // 更新session\n      await update({\n        ...session,\n        user: {\n          ...session?.user,\n          name: profileForm.name,\n          email: profileForm.email\n        }\n      });\n      \n      setSuccess('个人资料已更新');\n    } catch (err) {\n      setError('更新失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleNotificationSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // TODO: 实现更新通知设置API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSuccess('通知设置已更新');\n    } catch (err) {\n      setError('更新失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePrivacySubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // TODO: 实现更新隐私设置API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSuccess('隐私设置已更新');\n    } catch (err) {\n      setError('更新失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (status === 'loading') {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <LoadingSpinner size=\"lg\" className=\"py-20\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  if (!session) {\n    return null;\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center mb-8\">\n          <Link\n            href=\"/profile\"\n            className=\"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">账户设置</h1>\n            <p className=\"text-lg text-gray-600\">管理您的个人资料和偏好设置</p>\n          </div>\n        </div>\n\n        {/* Success/Error Messages */}\n        {success && (\n          <div className=\"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\">\n            <p className=\"text-green-800\">{success}</p>\n          </div>\n        )}\n        {error && (\n          <ErrorMessage\n            message={error}\n            onClose={() => setError('')}\n            className=\"mb-6\"\n          />\n        )}\n\n        <div className=\"space-y-8\">\n          {/* 个人资料 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center mb-6\">\n              <User className=\"h-6 w-6 text-gray-600 mr-3\" />\n              <h2 className=\"text-xl font-semibold text-gray-900\">个人资料</h2>\n            </div>\n\n            <form onSubmit={handleProfileSubmit} className=\"space-y-6\">\n              {/* 头像 */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  头像\n                </label>\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n                    {session.user?.image ? (\n                      <img\n                        src={session.user.image}\n                        alt={session.user.name || ''}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    ) : (\n                      <span className=\"text-xl font-medium text-gray-600\">\n                        {session.user?.name?.charAt(0) || 'U'}\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      type=\"button\"\n                      className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors\"\n                    >\n                      <Camera className=\"mr-2 h-4 w-4\" />\n                      更换头像\n                    </button>\n                    <button\n                      type=\"button\"\n                      className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors\"\n                    >\n                      <Trash2 className=\"mr-2 h-4 w-4\" />\n                      删除\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* 基本信息 */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    姓名\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    value={profileForm.name}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, name: e.target.value }))}\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    邮箱\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    value={profileForm.email}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, email: e.target.value }))}\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  个人简介\n                </label>\n                <textarea\n                  id=\"bio\"\n                  rows={3}\n                  value={profileForm.bio}\n                  onChange={(e) => setProfileForm(prev => ({ ...prev, bio: e.target.value }))}\n                  placeholder=\"介绍一下您自己...\"\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"website\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    个人网站\n                  </label>\n                  <input\n                    type=\"url\"\n                    id=\"website\"\n                    value={profileForm.website}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, website: e.target.value }))}\n                    placeholder=\"https://example.com\"\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    所在地\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"location\"\n                    value={profileForm.location}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, location: e.target.value }))}\n                    placeholder=\"城市, 国家\"\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex justify-end\">\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n                >\n                  {loading ? (\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  ) : (\n                    <Save className=\"mr-2 h-5 w-5\" />\n                  )}\n                  保存更改\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {/* 通知设置 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center mb-6\">\n              <Bell className=\"h-6 w-6 text-gray-600 mr-3\" />\n              <h2 className=\"text-xl font-semibold text-gray-900\">通知设置</h2>\n            </div>\n\n            <form onSubmit={handleNotificationSubmit} className=\"space-y-4\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">邮件通知</h3>\n                    <p className=\"text-sm text-gray-500\">接收重要更新的邮件通知</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.emailNotifications}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, emailNotifications: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">工具审核通知</h3>\n                    <p className=\"text-sm text-gray-500\">当您提交的工具审核状态变更时通知您</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.toolApprovalNotifications}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, toolApprovalNotifications: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">每周摘要</h3>\n                    <p className=\"text-sm text-gray-500\">接收每周的新工具和热门内容摘要</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.weeklyDigest}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, weeklyDigest: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">营销邮件</h3>\n                    <p className=\"text-sm text-gray-500\">接收产品更新和特别优惠信息</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.marketingEmails}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, marketingEmails: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end pt-4\">\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n                >\n                  {loading ? (\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  ) : (\n                    <Save className=\"mr-2 h-5 w-5\" />\n                  )}\n                  保存设置\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {/* 隐私设置 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center mb-6\">\n              <Shield className=\"h-6 w-6 text-gray-600 mr-3\" />\n              <h2 className=\"text-xl font-semibold text-gray-900\">隐私设置</h2>\n            </div>\n\n            <form onSubmit={handlePrivacySubmit} className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  个人资料可见性\n                </label>\n                <select\n                  value={privacySettings.profileVisibility}\n                  onChange={(e) => setPrivacySettings(prev => ({ ...prev, profileVisibility: e.target.value }))}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"public\">公开 - 任何人都可以查看</option>\n                  <option value=\"private\">私密 - 只有您可以查看</option>\n                </select>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">显示邮箱地址</h3>\n                    <p className=\"text-sm text-gray-500\">在您的公开资料中显示邮箱地址</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={privacySettings.showEmail}\n                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, showEmail: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">显示提交的工具</h3>\n                    <p className=\"text-sm text-gray-500\">在您的公开资料中显示您提交的工具</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={privacySettings.showSubmittedTools}\n                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, showSubmittedTools: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end\">\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n                >\n                  {loading ? (\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  ) : (\n                    <Save className=\"mr-2 h-5 w-5\" />\n                  )}\n                  保存设置\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAqBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,SAAS;IACT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,OAAO;QACP,KAAK;QACL,SAAS;QACT,UAAU;IACZ;IAEA,OAAO;IACP,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/D,oBAAoB;QACpB,2BAA2B;QAC3B,cAAc;QACd,iBAAiB;IACnB;IAEA,OAAO;IACP,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,mBAAmB;QACnB,WAAW;QACX,oBAAoB;IACtB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,mBAAmB,SAAS,MAAM;YAC/C,eAAe;gBACb,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;gBAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;gBAC7B,KAAK;gBACL,SAAS;gBACT,UAAU;YACZ;QACF;IACF,GAAG;QAAC;QAAQ;QAAS;KAAO;IAE5B,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,oBAAoB;YACpB,+DAA+D;YAE/D,UAAU;YACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,YAAY;YACZ,MAAM,OAAO;gBACX,GAAG,OAAO;gBACV,MAAM;oBACJ,GAAG,SAAS,IAAI;oBAChB,MAAM,YAAY,IAAI;oBACtB,OAAO,YAAY,KAAK;gBAC1B;YACF;YAEA,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;oBAAC,MAAK;oBAAK,WAAU;;;;;;;;;;;;;;;;IAI5C;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;gBAKxC,yBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAkB;;;;;;;;;;;gBAGlC,uBACC,8OAAC,kIAAA,CAAA,UAAY;oBACX,SAAS;oBACT,SAAS,IAAM,SAAS;oBACxB,WAAU;;;;;;8BAId,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;;8CAGtD,8OAAC;oCAAK,UAAU;oCAAqB,WAAU;;sDAE7C,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,IAAI,EAAE,sBACb,8OAAC;gEACC,KAAK,QAAQ,IAAI,CAAC,KAAK;gEACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;gEAC1B,WAAU;;;;;qFAGZ,8OAAC;gEAAK,WAAU;0EACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;sEAIxC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,WAAU;;sFAEV,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGrC,8OAAC;oEACC,MAAK;oEACL,WAAU;;sFAEV,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA+C;;;;;;sEAG/E,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO,YAAY,IAAI;4DACvB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC1E,WAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO,YAAY,KAAK;4DACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC3E,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAM,WAAU;8DAA+C;;;;;;8DAG9E,8OAAC;oDACC,IAAG;oDACH,MAAM;oDACN,OAAO,YAAY,GAAG;oDACtB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACzE,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAA+C;;;;;;sEAGlF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO,YAAY,OAAO;4DAC1B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC7E,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAA+C;;;;;;sEAGnF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO,YAAY,QAAQ;4DAC3B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC9E,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;;oDAET,wBACC,8OAAC,oIAAA,CAAA,UAAc;wDAAC,MAAK;wDAAK,WAAU;;;;;6EAEpC,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAChB;;;;;;;;;;;;;;;;;;;;;;;;sCAQV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;;8CAGtD,8OAAC;oCAAK,UAAU;oCAA0B,WAAU;;sDAClD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,qBAAqB,kBAAkB;oEAChD,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEACnG,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,qBAAqB,yBAAyB;oEACvD,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,2BAA2B,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEAC1G,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,qBAAqB,YAAY;oEAC1C,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,cAAc,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEAC7F,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,qBAAqB,eAAe;oEAC7C,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEAChG,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAKrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;;oDAET,wBACC,8OAAC,oIAAA,CAAA,UAAc;wDAAC,MAAK;wDAAK,WAAU;;;;;6EAEpC,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAChB;;;;;;;;;;;;;;;;;;;;;;;;sCAQV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;;8CAGtD,8OAAC;oCAAK,UAAU;oCAAqB,WAAU;;sDAC7C,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO,gBAAgB,iBAAiB;oDACxC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC3F,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;;;;;;;;;;;;;sDAI5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,gBAAgB,SAAS;oEAClC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,WAAW,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEACrF,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;8EAClD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,gBAAgB,kBAAkB;oEAC3C,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEAC9F,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAKrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;;oDAET,wBACC,8OAAC,oIAAA,CAAA,UAAc;wDAAC,MAAK;wDAAK,WAAU;;;;;6EAEpC,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB", "debugId": null}}]}